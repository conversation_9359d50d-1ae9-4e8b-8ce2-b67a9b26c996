"use client";

import { LanguageSwitcher } from "@/components/language-switcher";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useScrollAnimations } from "@/hooks/use-scroll-animations";
import { useTranslations } from "@/hooks/use-translations";
import {
	Award,
	Calendar,
	Globe,
	Heart,
	Hotel,
	Info,
	Mail,
	MapPin,
	Menu,
	Package,
	Phone,
	Shield,
	Star,
	Target,
	Users,
	Utensils,
	Wrench,
	X,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";

export default function GranFondoPage() {
	const [scrollY, setScrollY] = useState(0);
	const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
	const { t } = useTranslations();

	// Initialize scroll animations
	useScrollAnimations();

	useEffect(() => {
		const handleScroll = () => setScrollY(window.scrollY);
		window.addEventListener("scroll", handleScroll);
		return () => window.removeEventListener("scroll", handleScroll);
	}, []);
	return (
		<div className="flex flex-col min-h-[100dvh] bg-light-gray text-brand-charcoal">
			<header className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md shadow-lg border-b border-light-gray">
				<div className="container mx-auto flex h-20 items-center justify-between px-4 md:px-6">
					<Link href="#accueil" className="flex items-center gap-3 group">
						<div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-sm">
							<Image
								src="/odass-new-logo.png"
								alt="Logo Gran Fondo Îles de Guadeloupe"
								width={40}
								height={40}
								className="rounded-lg"
							/>
						</div>
						<span className="font-bold text-lg sm:text-xl text-brand-blue whitespace-nowrap">
							Gran Fondo <span className="hidden sm:inline text-brand-orange">Îles de Guadeloupe</span>
						</span>
					</Link>
					<nav className="hidden lg:flex items-center gap-8 text-sm font-medium">
						<Link
							href="#epreuve"
							className="hover:text-brand-orange transition-colors duration-300 relative group"
						>
							{t("navigation.event")}
							<span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-brand-orange transition-all duration-300 group-hover:w-full"></span>
						</Link>
						<Link
							href="#programme"
							className="hover:text-brand-orange transition-colors duration-300 relative group"
						>
							{t("navigation.program")}
							<span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-brand-orange transition-all duration-300 group-hover:w-full"></span>
						</Link>
						<Link
							href="#participation"
							className="hover:text-brand-orange transition-colors duration-300 relative group"
						>
							{t("navigation.participation")}
							<span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-brand-orange transition-all duration-300 group-hover:w-full"></span>
						</Link>
						<Link
							href="#infos-pratiques"
							className="hover:text-brand-orange transition-colors duration-300 relative group"
						>
							{t("navigation.practicalInfo")}
							<span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-brand-orange transition-all duration-300 group-hover:w-full"></span>
						</Link>
						<Link
							href="#partenaires"
							className="hover:text-brand-orange transition-colors duration-300 relative group"
						>
							{t("navigation.partners")}
							<span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-brand-orange transition-all duration-300 group-hover:w-full"></span>
						</Link>
						<Link
							href="#contact"
							className="hover:text-brand-orange transition-colors duration-300 relative group"
						>
							{t("navigation.contact")}
							<span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-brand-orange transition-all duration-300 group-hover:w-full"></span>
						</Link>
					</nav>
					<div className="flex items-center gap-4">
						<LanguageSwitcher />
						<Button
							asChild
							className="hidden lg:flex bg-gradient-to-r from-brand-orange to-orange-500 hover:from-orange-500 hover:to-brand-orange text-white shadow-lg hover:shadow-xl transition-all duration-300"
						>
							<a
								href="https://www.sportsnconnect.com/calendrier-evenements/view/29239/gran-fondo-guadeloupe"
								target="_blank"
								rel="noopener noreferrer"
							>
								{t("navigation.register")}
							</a>
						</Button>
						<Button
							variant="ghost"
							size="sm"
							className="lg:hidden"
							onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
						>
							{mobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
						</Button>
					</div>
				</div>
				{/* Mobile Menu */}
				{mobileMenuOpen && (
					<div className="lg:hidden bg-white/95 backdrop-blur-md border-t border-light-gray">
						<nav className="container mx-auto px-4 py-4 flex flex-col gap-4">
							<Link
								href="#epreuve"
								className="hover:text-brand-orange transition-colors duration-300 py-2"
								onClick={() => setMobileMenuOpen(false)}
							>
								{t("navigation.event")}
							</Link>
							<Link
								href="#programme"
								className="hover:text-brand-orange transition-colors duration-300 py-2"
								onClick={() => setMobileMenuOpen(false)}
							>
								{t("navigation.program")}
							</Link>
							<Link
								href="#participation"
								className="hover:text-brand-orange transition-colors duration-300 py-2"
								onClick={() => setMobileMenuOpen(false)}
							>
								{t("navigation.participation")}
							</Link>
							<Link
								href="#infos-pratiques"
								className="hover:text-brand-orange transition-colors duration-300 py-2"
								onClick={() => setMobileMenuOpen(false)}
							>
								{t("navigation.practicalInfo")}
							</Link>
							<Link
								href="#partenaires"
								className="hover:text-brand-orange transition-colors duration-300 py-2"
								onClick={() => setMobileMenuOpen(false)}
							>
								{t("navigation.partners")}
							</Link>
							<Link
								href="#contact"
								className="hover:text-brand-orange transition-colors duration-300 py-2"
								onClick={() => setMobileMenuOpen(false)}
							>
								{t("navigation.contact")}
							</Link>
							<Button
								asChild
								className="mt-2 bg-gradient-to-r from-brand-orange to-orange-500 hover:from-orange-500 hover:to-brand-orange text-white shadow-lg hover:shadow-xl transition-all duration-300"
							>
								<a
									href="https://www.sportsnconnect.com/calendrier-evenements/view/29239/gran-fondo-guadeloupe"
									target="_blank"
									rel="noopener noreferrer"
								>
									{t("navigation.register")}
								</a>
							</Button>
						</nav>
					</div>
				)}
			</header>

			<main className="flex-1">
				<section
					id="accueil"
					className="relative w-full pt-24 lg:pt-28 pb-16 md:pb-24 text-slate-800 overflow-hidden"
					style={{
						backgroundColor: "white",
						backgroundImage: `url('/images/background-pattern.png')`,
						backgroundRepeat: "no-repeat",
						backgroundSize: "cover",
						backgroundPosition: "center",
					}}
				>
					<div
						className="absolute inset-0 z-10"
						style={{
							background:
								"linear-gradient(to right, rgba(148, 163, 184, 0.12) 0%, rgba(148, 163, 184, 0.08) 20%, rgba(148, 163, 184, 0.04) 35%, transparent 40%)",
						}}
					></div>
					<div className="container mx-auto px-4 md:px-6 grid lg:grid-cols-2 gap-8 items-center relative z-20">
						<div className="flex flex-col gap-6 text-center lg:text-left">
							<div className="inline-flex items-center gap-2 bg-brand-orange/20 backdrop-blur-sm px-4 py-2 rounded-full text-brand-orange font-semibold text-sm w-fit mx-auto lg:mx-0 animate-scale-in animate-stagger-1">
								<Star className="w-4 h-4" />
								{t("hero.badge")}
							</div>
							<h1 className="text-4xl md:text-5xl lg:text-6xl font-extrabold tracking-tight leading-tight animate-fade-in-up animate-stagger-2">
								{t("hero.title")}{" "}
								<span
									className="text-brand-orange italic inline-block"
									style={{
										background: "linear-gradient(to right, #ff6b35, #fbbf24)",
										WebkitBackgroundClip: "text",
										WebkitTextFillColor: "transparent",
										backgroundClip: "text",
										paddingBottom: "4px",
										paddingTop: "2px",
										paddingLeft: "0px",
										paddingRight: "8px",
										lineHeight: "1.3",
										display: "inline-block",
										overflow: "visible",
										minWidth: "fit-content",
									}}
								>
									{t("hero.titleHighlight")}
								</span>
							</h1>
							<p className="text-lg md:text-xl text-slate-600 max-w-xl mx-auto lg:mx-0 leading-relaxed animate-fade-in-up animate-stagger-3">
								{t("hero.subtitle")}
							</p>
							<div className="flex flex-col sm:flex-row items-center justify-center lg:justify-start gap-4 mt-4 animate-fade-in-up animate-stagger-4">
								<div className="inline-flex items-center gap-3 bg-white/20 backdrop-blur-md px-8 py-4 rounded-3xl border border-white/30 hover:bg-white/30 transition-all duration-300 text-lg font-bold">
									<div className="w-12 h-12 bg-brand-orange rounded-full flex items-center justify-center">
										<Calendar className="w-6 h-6 text-white" />
									</div>
									<span className="text-xl font-bold">{t("hero.date")}</span>
								</div>
								<div className="flex items-center gap-3 bg-white/20 backdrop-blur-sm px-6 py-3 rounded-2xl border border-white/30 hover:bg-white/30 transition-all duration-300">
									<div className="w-10 h-10 bg-palm-green rounded-full flex items-center justify-center">
										<MapPin className="w-5 h-5 text-white" />
									</div>
									<span className="font-semibold">{t("hero.location")}</span>
								</div>
							</div>
							<div className="mt-8 flex justify-center lg:justify-start animate-fade-in-up animate-stagger-5">
								<Button
									size="lg"
									asChild
									className="bg-gradient-to-r from-brand-orange to-orange-500 hover:from-orange-500 hover:to-brand-orange text-white text-lg font-bold px-10 py-6 rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-300 hover:scale-105"
								>
									<a
										href="https://www.sportsnconnect.com/calendrier-evenements/view/29239/gran-fondo-guadeloupe"
										target="_blank"
										rel="noopener noreferrer"
									>
										{t("hero.registerButton")}
									</a>
								</Button>
							</div>
						</div>
						<div className="relative h-96 sm:h-[500px] lg:h-[700px] xl:h-[800px] w-full bg-transparent shadow-none overflow-visible animate-slide-in-right animate-stagger-2">
							<div className="absolute inset-0 from-brand-blue/20 to-transparent rounded-3xl"></div>
							<div
								className="relative w-full h-full"
								style={{
									transform: `translateY(${scrollY * 0.05}px) scale(1.4)`,
									transition: "transform 0.1s ease-out",
									transformOrigin: "center center",
								}}
							>
								<Image
									src="/images/home-hero.png"
									alt="Cyclistes participant au Gran Fondo en Guadeloupe"
									fill
									className="object-contain drop-shadow-2xl"
								/>
							</div>
						</div>
					</div>
				</section>

				<section
					id="epreuve"
					className="py-16 md:py-24 bg-gradient-to-br from-brand-white to-light-gray relative overflow-hidden"
					style={{
						backgroundColor: "#059669",
						backgroundImage: `linear-gradient(to right, #059669 0%, #047857 100%), url('/images/background-pattern.png')`,
						backgroundRepeat: "no-repeat",
						backgroundSize: "cover",
						backgroundPosition: "center",
						backgroundBlendMode: "overlay",
					}}
				>
					<div className="container mx-auto px-4 md:px-6 grid lg:grid-cols-2 gap-12 items-center">
						<div className="order-1 lg:order-1">
							<div
								id="epreuve-badge"
								className="inline-flex items-center gap-2 bg-brand-orange px-4 py-2 rounded-full text-white font-semibold text-sm mb-6 animate-scroll-scale animate-scroll-stagger-1"
							>
								<Globe className="w-4 h-4" />
								{t("event.badge")}
							</div>
							<h2 className="text-3xl md:text-4xl font-bold text-white mb-6 leading-tight animate-scroll-fade-up animate-scroll-stagger-2">
								{t("event.title")}
							</h2>
							<div className="space-y-6">
								<Card className="bg-gradient-to-r from-brand-white to-sky-gradient/5 border-l-4 border-brand-sky shadow-lg animate-scroll-fade-up animate-scroll-stagger-3">
									<CardContent className="p-6">
										<div className="flex items-start gap-4">
											<div className="w-12 h-12 bg-gradient-to-br from-brand-sky to-sky-gradient rounded-xl flex items-center justify-center flex-shrink-0">
												<Target className="w-6 h-6 text-white" />
											</div>
											<div>
												<h3 className="font-bold text-lg text-brand-blue mb-2">
													{t("event.qualification.title")}
												</h3>
												<p className="text-dark-gray leading-relaxed">
													{t("event.qualification.description")}
												</p>
											</div>
										</div>
									</CardContent>
								</Card>
								<Card className="bg-gradient-to-r from-brand-white to-brand-orange/5 border-l-4 border-brand-orange shadow-lg animate-scroll-fade-up animate-scroll-stagger-4">
									<CardContent className="p-6">
										<div className="flex items-start gap-4">
											<div className="w-12 h-12 bg-gradient-to-br from-brand-orange to-orange-500 rounded-xl flex items-center justify-center flex-shrink-0">
												<Heart className="w-6 h-6 text-white" />
											</div>
											<div>
												<h3 className="font-bold text-lg text-brand-blue mb-2">
													{t("event.cycling.title")}
												</h3>
												<p className="text-dark-gray leading-relaxed">
													{t("event.cycling.description")}
												</p>
											</div>
										</div>
									</CardContent>
								</Card>
							</div>
						</div>
						<div className="order-2 lg:order-2 flex items-start">
							<div className="animate-scroll-slide-right animate-scroll-stagger-1 w-full">
								<div className="relative">
									<div className="absolute inset-0 bg-gradient-to-br from-brand-sky/20 to-palm-green/20 rounded-3xl transform rotate-3"></div>
									<Image
										src="/guadeloupe-coastline.png"
										alt="Paysage côtier de la Guadeloupe"
										width={600}
										height={400}
										className="rounded-3xl shadow-2xl w-full relative z-10 hover:scale-105 transition-transform duration-500"
									/>
								</div>
							</div>
						</div>
					</div>
				</section>

				<section
					id="programme"
					className="py-16 md:py-24 bg-gradient-to-br from-light-gray to-brand-white relative overflow-hidden"
					style={{
						backgroundImage: `linear-gradient(to right, rgba(148, 163, 184, 0.12) 0%, rgba(148, 163, 184, 0.08) 20%, rgba(148, 163, 184, 0.04) 35%, transparent 40%), url('/images/background-pattern.png')`,
						backgroundRepeat: "no-repeat",
						backgroundSize: "cover",
						backgroundPosition: "center",
					}}
				>
					<div className="container mx-auto px-4 md:px-6">
						<div className="text-center mb-12">
							<div
								id="programme-badge"
								className="inline-flex items-center gap-2 bg-brand-orange/10 px-4 py-2 rounded-full text-brand-orange font-semibold text-sm mb-4 animate-scroll-scale animate-scroll-stagger-1"
							>
								<Calendar className="w-4 h-4" />
								{t("program.badge")}
							</div>
							<h2 className="text-3xl md:text-4xl font-bold text-brand-blue mb-4 animate-scroll-fade-up animate-scroll-stagger-2">
								{t("program.title")}
							</h2>
							<p className="text-lg text-dark-gray animate-scroll-fade-up animate-scroll-stagger-3">
								{t("program.subtitle")}
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
							<Card className="bg-gradient-to-br from-brand-white to-palm-green/10 shadow-2xl border-0 hover:shadow-3xl transition-all duration-300 hover:-translate-y-2 group overflow-hidden animate-scroll-slide-left animate-scroll-stagger-1">
								<div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-palm-green to-mountain-green"></div>
								<CardHeader className="pb-4">
									<CardTitle className="flex items-center justify-between text-2xl font-bold">
										<span className="text-brand-blue">{t("program.day0.title")}</span>
										<div className="w-16 h-16 bg-gradient-to-br from-palm-green to-mountain-green text-white rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
											<Package className="w-8 h-8" />
										</div>
									</CardTitle>
								</CardHeader>
								<CardContent className="space-y-4">
									<div className="bg-palm-green/10 p-4 rounded-xl">
										<h3 className="font-bold text-lg text-brand-blue">{t("program.day0.event")}</h3>
										<p className="text-dark-gray">{t("program.day0.description")}</p>
									</div>
									<div className="bg-brand-white/50 p-4 rounded-xl">
										<p className="font-semibold text-brand-blue">{t("common.location")}</p>
										<p className="text-dark-gray">{t("program.day0.location")}</p>
									</div>
								</CardContent>
							</Card>
							<Card className="bg-gradient-to-br from-brand-white to-brand-sky/10 shadow-2xl border-0 hover:shadow-3xl transition-all duration-300 hover:-translate-y-2 group overflow-hidden animate-scroll-slide-left animate-scroll-stagger-2">
								<div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-brand-blue to-brand-sky"></div>
								<CardHeader className="pb-4">
									<CardTitle className="flex items-center justify-between text-2xl font-bold">
										<span className="text-brand-blue">{t("program.day1.title")}</span>
										<div className="w-16 h-16 bg-gradient-to-br from-brand-blue to-brand-sky text-white rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
											<Calendar className="w-8 h-8" />
										</div>
									</CardTitle>
								</CardHeader>
								<CardContent className="space-y-4">
									<div className="bg-brand-sky/10 p-4 rounded-xl">
										<h3 className="font-bold text-lg text-brand-blue">{t("program.day1.event")}</h3>
										<p className="text-dark-gray">{t("program.day1.distance")}</p>
									</div>
									<div className="grid grid-cols-2 gap-4">
										<div>
											<p className="font-semibold text-brand-blue">
												{t("program.day1.departure")}
											</p>
											<p className="text-dark-gray">{t("program.day1.departureTime")}</p>
										</div>
										<div>
											<p className="font-semibold text-brand-blue">{t("program.day1.arrival")}</p>
											<p className="text-dark-gray">{t("program.day1.arrivalTime")}</p>
										</div>
									</div>
									<div className="bg-palm-green/10 p-4 rounded-xl">
										<p className="font-semibold text-brand-blue">{t("common.location")}</p>
										<p className="text-dark-gray">{t("program.day1.location")}</p>
									</div>
								</CardContent>
							</Card>
							<Card className="bg-gradient-to-br from-brand-white to-brand-orange/10 shadow-2xl border-0 hover:shadow-3xl transition-all duration-300 hover:-translate-y-2 group overflow-hidden animate-scroll-slide-right animate-scroll-stagger-3">
								<div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-brand-orange to-orange-500"></div>
								<CardHeader className="pb-4">
									<CardTitle className="flex items-center justify-between text-2xl font-bold">
										<span className="text-brand-blue">{t("program.day2.title")}</span>
										<div className="w-16 h-16 bg-gradient-to-br from-brand-orange to-orange-500 text-white rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
											<Award className="w-8 h-8" />
										</div>
									</CardTitle>
								</CardHeader>
								<CardContent className="space-y-4">
									<div className="bg-brand-orange/10 p-4 rounded-xl">
										<h3 className="font-bold text-lg text-brand-blue">{t("program.day2.event")}</h3>
										<p className="text-dark-gray">{t("program.day2.distance")}</p>
									</div>
									<div className="grid grid-cols-2 gap-4">
										<div>
											<p className="font-semibold text-brand-blue">
												{t("program.day2.departure")}
											</p>
											<p className="text-dark-gray">{t("program.day2.departureTime")}</p>
										</div>
										<div>
											<p className="font-semibold text-brand-blue">{t("program.day2.arrival")}</p>
											<p className="text-dark-gray">{t("program.day2.arrivalTime")}</p>
										</div>
									</div>
									<div className="bg-mountain-green/10 p-4 rounded-xl">
										<p className="font-semibold text-brand-blue">{t("common.place")}</p>
										<p className="text-dark-gray">{t("program.day2.location")}</p>
									</div>
									<div className="bg-palm-green/10 p-4 rounded-xl">
										<p className="font-semibold text-brand-blue">{t("program.day2.keyPoint")}</p>
										<p className="text-dark-gray">{t("program.day2.keyPointDescription")}</p>
									</div>
								</CardContent>
							</Card>
						</div>
					</div>
				</section>

				<section
					id="participation"
					className="py-16 md:py-24 bg-gradient-to-br from-brand-white to-light-gray relative overflow-hidden"
					style={{
						backgroundColor: "#059669",
						backgroundImage: `linear-gradient(to right, #059669 0%, #047857 100%), url('/images/background-pattern.png')`,
						backgroundRepeat: "no-repeat",
						backgroundSize: "cover",
						backgroundPosition: "center",
						backgroundBlendMode: "overlay",
					}}
				>
					<div className="container mx-auto px-4 md:px-6">
						<div className="text-center mb-12">
							<div
								id="participation-badge"
								className="inline-flex items-center gap-2 bg-brand-orange px-4 py-2 rounded-full text-white font-semibold text-sm mb-4 animate-scroll-scale animate-scroll-stagger-1"
							>
								<Users className="w-4 h-4" />
								{t("participation.badge")}
							</div>
							<h2 className="text-3xl md:text-4xl font-bold text-white mb-4 animate-scroll-fade-up animate-scroll-stagger-2">
								{t("participation.title")}
							</h2>
							<p className="text-lg text-white/90 animate-scroll-fade-up animate-scroll-stagger-3">
								{t("participation.subtitle")}
							</p>
						</div>
						<div className="grid lg:grid-cols-3 gap-8">
							<Card className="bg-gradient-to-br from-brand-white to-brand-sky/10 border-0 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 group animate-scroll-scale animate-scroll-stagger-1">
								<CardHeader className="pb-4">
									<div className="w-16 h-16 bg-gradient-to-br from-brand-blue to-brand-sky rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
										<Users className="text-white w-8 h-8" />
									</div>
									<CardTitle className="text-xl font-bold text-brand-blue">
										{t("participation.conditions.title")}
									</CardTitle>
								</CardHeader>
								<CardContent>
									<ul className="space-y-3 text-dark-gray">
										<li className="flex items-start gap-3">
											<div className="w-2 h-2 bg-brand-orange rounded-full mt-2 flex-shrink-0"></div>
											<span>{t("participation.conditions.items.0")}</span>
										</li>
										<li className="flex items-start gap-3">
											<div className="w-2 h-2 bg-brand-orange rounded-full mt-2 flex-shrink-0"></div>
											<span>{t("participation.conditions.items.1")}</span>
										</li>
										<li className="flex items-start gap-3">
											<div className="w-2 h-2 bg-brand-orange rounded-full mt-2 flex-shrink-0"></div>
											<span>{t("participation.conditions.items.2")}</span>
										</li>
									</ul>
								</CardContent>
							</Card>

							<Card className="bg-gradient-to-br from-brand-white to-brand-orange/10 border-0 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 group animate-scroll-scale animate-scroll-stagger-2">
								<CardHeader className="pb-4">
									<div className="w-16 h-16 bg-gradient-to-br from-brand-orange to-orange-500 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
										<Star className="text-white w-8 h-8" />
									</div>
									<CardTitle className="text-xl font-bold text-brand-blue">
										{t("participation.ageCategories.title")}
									</CardTitle>
								</CardHeader>
								<CardContent>
									<div className="grid grid-cols-2 gap-2 text-dark-gray">
										{[
											"19–34",
											"35–39",
											"40–44",
											"45–49",
											"50–54",
											"55–59",
											"60–64",
											"65–69",
											"70–74",
											"75–79 ans",
										].map((age, index) => (
											<div key={index} className="flex items-center gap-2 py-1">
												<div className="w-2 h-2 bg-brand-orange rounded-full flex-shrink-0"></div>
												<span className="text-sm font-medium">{age}</span>
											</div>
										))}
									</div>
								</CardContent>
							</Card>

							<Card className="bg-gradient-to-br from-brand-white to-red-50 border-0 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 group animate-scroll-scale animate-scroll-stagger-3">
								<CardHeader className="pb-4">
									<div className="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
										<Info className="text-white w-8 h-8" />
									</div>
									<CardTitle className="text-xl font-bold text-brand-blue">
										{t("participation.restrictions.title")}
									</CardTitle>
								</CardHeader>
								<CardContent>
									<ul className="space-y-3 text-dark-gray">
										<li className="flex items-start gap-3">
											<div className="w-2 h-2 bg-brand-orange rounded-full mt-2 flex-shrink-0"></div>
											<span>{t("participation.restrictions.items.0")}</span>
										</li>
										<li className="flex items-start gap-3">
											<div className="w-2 h-2 bg-brand-orange rounded-full mt-2 flex-shrink-0"></div>
											<span>{t("participation.restrictions.items.1")}</span>
										</li>
										<li className="flex items-start gap-3">
											<div className="w-2 h-2 bg-brand-orange rounded-full mt-2 flex-shrink-0"></div>
											<span>{t("participation.restrictions.items.2")}</span>
										</li>
									</ul>
								</CardContent>
							</Card>
						</div>
						<div className="text-center mt-12">
							<Button
								size="lg"
								asChild
								className="bg-gradient-to-r from-brand-orange to-orange-500 hover:from-orange-500 hover:to-brand-orange text-white font-bold shadow-lg hover:shadow-xl transition-all duration-300 px-8 py-6 rounded-2xl"
							>
								<a
									href="https://www.sportsnconnect.com/calendrier-evenements/view/29239/gran-fondo-guadeloupe"
									target="_blank"
									rel="noopener noreferrer"
								>
									{t("participation.registerButton")}
								</a>
							</Button>
						</div>
					</div>
				</section>

				<section
					id="infos-pratiques"
					className="py-16 md:py-24 bg-gradient-to-br from-light-gray to-brand-white relative overflow-hidden"
					style={{
						backgroundImage: `linear-gradient(to right, rgba(148, 163, 184, 0.12) 0%, rgba(148, 163, 184, 0.08) 20%, rgba(148, 163, 184, 0.04) 35%, transparent 40%), url('/images/background-pattern.png')`,
						backgroundRepeat: "no-repeat",
						backgroundSize: "cover",
						backgroundPosition: "center",
					}}
				>
					<div className="container mx-auto px-4 md:px-6">
						<div className="text-center mb-12">
							<div
								id="infos-pratiques-badge"
								className="inline-flex items-center gap-2 bg-palm-green/10 px-4 py-2 rounded-full text-palm-green font-semibold text-sm mb-4 animate-scroll-scale animate-scroll-stagger-1"
							>
								<Info className="w-4 h-4" />
								{t("practicalInfo.badge")}
							</div>
							<h2 className="text-3xl md:text-4xl font-bold text-brand-blue mb-4 animate-scroll-fade-up animate-scroll-stagger-2">
								{t("practicalInfo.title")}
							</h2>
							<p className="text-lg text-dark-gray animate-scroll-fade-up animate-scroll-stagger-3">
								{t("practicalInfo.subtitle")}
							</p>
						</div>
						<div className="grid lg:grid-cols-3 gap-8">
							<Card className="bg-gradient-to-br from-brand-white to-palm-green/10 border-0 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 group animate-scroll-slide-left animate-scroll-stagger-1">
								<CardHeader className="pb-4">
									<div className="w-16 h-16 bg-gradient-to-br from-palm-green to-mountain-green rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
										<Package className="text-white w-8 h-8" />
									</div>
									<CardTitle className="text-xl font-bold text-brand-blue">
										{t("practicalInfo.included.title")}
									</CardTitle>
								</CardHeader>
								<CardContent className="space-y-3 text-dark-gray">
									<p className="flex items-center gap-3 p-2 bg-brand-white/50 rounded-lg">
										<Award className="w-5 h-5 text-brand-orange flex-shrink-0" />
										<span>{t("practicalInfo.included.items.0")}</span>
									</p>
									<p className="flex items-center gap-3 p-2 bg-brand-white/50 rounded-lg">
										<Shield className="w-5 h-5 text-brand-orange flex-shrink-0" />
										<span>{t("practicalInfo.included.items.1")}</span>
									</p>
									<p className="flex items-center gap-3 p-2 bg-brand-white/50 rounded-lg">
										<Utensils className="w-5 h-5 text-brand-orange flex-shrink-0" />
										<span>{t("practicalInfo.included.items.2")}</span>
									</p>
									<p className="flex items-center gap-3 p-2 bg-brand-white/50 rounded-lg">
										<Wrench className="w-5 h-5 text-brand-orange flex-shrink-0" />
										<span>{t("practicalInfo.included.items.3")}</span>
									</p>
									<p className="flex items-center gap-3 p-2 bg-brand-white/50 rounded-lg">
										<Star className="w-5 h-5 text-brand-orange flex-shrink-0" />
										<span>{t("practicalInfo.included.items.4")}</span>
									</p>
								</CardContent>
							</Card>
							<Card className="bg-gradient-to-br from-brand-white to-brand-sky/10 border-0 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 group animate-scroll-fade-up animate-scroll-stagger-2">
								<CardHeader className="pb-4">
									<div className="w-16 h-16 bg-gradient-to-br from-brand-sky to-sky-gradient rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
										<Hotel className="text-white w-8 h-8" />
									</div>
									<CardTitle className="text-xl font-bold text-brand-blue">
										{t("practicalInfo.accommodation.title")}
									</CardTitle>
								</CardHeader>
								<CardContent className="space-y-4 text-dark-gray">
									<div className="p-4 bg-palm-green/10 rounded-xl">
										<p className="font-semibold text-brand-blue mb-1">Hébergement :</p>
										<p>{t("practicalInfo.accommodation.accommodationInfo")}</p>
									</div>
									<div className="p-4 bg-brand-orange/10 rounded-xl">
										<p className="font-semibold text-brand-blue mb-1">Transport :</p>
										<p>{t("practicalInfo.accommodation.transportInfo")}</p>
									</div>
								</CardContent>
							</Card>
							<div className="lg:col-span-1">
								<div className="relative h-full min-h-[400px] animate-scroll-slide-right animate-scroll-stagger-3">
									<Image
										src="/guadeloupe-hotel-resort.png"
										alt="Hôtel en Guadeloupe"
										width={400}
										height={400}
										className="rounded-3xl shadow-2xl w-full h-full object-cover relative z-10 hover:scale-105 transition-transform duration-500"
									/>
								</div>
							</div>
						</div>
					</div>
				</section>

				<section
					id="partenaires"
					className="py-16 md:py-24 bg-gradient-to-br from-brand-white to-light-gray relative overflow-hidden"
					style={{
						backgroundColor: "#059669",
						backgroundImage: `linear-gradient(to right, #059669 0%, #047857 100%), url('/images/background-pattern.png')`,
						backgroundRepeat: "no-repeat",
						backgroundSize: "cover",
						backgroundPosition: "center",
						backgroundBlendMode: "overlay",
					}}
				>
					<div className="container mx-auto px-4 md:px-6 text-center">
						<div>
							<div
								id="partenaires-badge"
								className="inline-flex items-center gap-2 bg-brand-orange px-4 py-2 rounded-full text-white font-semibold text-sm mb-4 animate-scroll-scale animate-scroll-stagger-1"
							>
								<Star className="w-4 h-4" />
								{t("partners.badge")}
							</div>
							<h2 className="text-3xl md:text-4xl font-bold text-white mb-4 animate-scroll-fade-up animate-scroll-stagger-2">
								{t("partners.title")}
							</h2>
							<p className="text-lg text-white/90 mb-12 animate-scroll-fade-up animate-scroll-stagger-3">
								{t("partners.subtitle")}
							</p>
						</div>
						<div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-8 items-center">
							<Card className="bg-gradient-to-br from-brand-white to-brand-blue/5 p-6 border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group animate-scroll-scale animate-scroll-stagger-1">
								<div className="text-center">
									<div className="w-20 h-20 bg-white rounded-2xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300 shadow-sm">
										<Image
											src="/odass-new-logo.png"
											alt="Logo OD'ASS"
											width={60}
											height={60}
											className="rounded-xl"
										/>
									</div>
									<p className="font-bold text-brand-blue text-sm">OD'ASS</p>
									<p className="text-xs text-dark-gray">{t("partners.organizer")}</p>
								</div>
							</Card>
							<Card className="bg-gradient-to-br from-brand-white to-brand-orange/5 p-6 border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group animate-scroll-scale animate-scroll-stagger-2">
								<div className="text-center">
									<div className="w-20 h-20 bg-gradient-to-br from-brand-orange to-orange-500 rounded-2xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
										<Image
											src="/team-cama-ccd-logo.png"
											alt="Logo Team Cama CCD"
											width={40}
											height={40}
											className="rounded-xl"
										/>
									</div>
									<p className="font-bold text-brand-blue text-sm">Team Cama CCD</p>
								</div>
							</Card>
							<Card className="bg-gradient-to-br from-brand-white to-brand-orange/5 p-6 border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group animate-scroll-scale animate-scroll-stagger-3">
								<div className="text-center">
									<div className="w-20 h-20 bg-gradient-to-br from-brand-sky to-sky-gradient rounded-2xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
										<Image
											src="/golazo-sports-logo.png"
											alt="Logo Golazo Sports"
											width={40}
											height={40}
											className="rounded-xl"
										/>
									</div>
									<p className="font-bold text-brand-blue text-sm">Golazo Sports</p>
								</div>
							</Card>
							<Card className="bg-gradient-to-br from-brand-white to-brand-sky/5 p-6 border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group animate-scroll-scale animate-scroll-stagger-4">
								<div className="text-center">
									<div className="w-20 h-20 bg-white rounded-2xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
										<Image
											src="/france-tv.png"
											alt="Logo France Télévisions"
											width={40}
											height={40}
											className="rounded-xl"
										/>
									</div>
									<p className="font-bold text-brand-blue text-sm">France Télévisions</p>
								</div>
							</Card>
							<Card className="bg-gradient-to-br from-brand-white to-red-50 p-6 border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group animate-scroll-scale animate-scroll-stagger-5">
								<div className="text-center">
									<div className="w-20 h-20 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
										<Image
											src="/Eurosport-Logo.png"
											alt="Logo Eurosport"
											width={40}
											height={40}
											className="rounded-xl"
										/>
									</div>
									<p className="font-bold text-brand-blue text-sm">Eurosport</p>
								</div>
							</Card>
						</div>
					</div>
				</section>

				<section
					id="contact"
					className="py-16 md:py-24 bg-gradient-to-br from-light-gray to-brand-white relative overflow-hidden"
					style={{
						backgroundImage: `linear-gradient(to right, rgba(148, 163, 184, 0.12) 0%, rgba(148, 163, 184, 0.08) 20%, rgba(148, 163, 184, 0.04) 35%, transparent 40%), url('/images/background-pattern.png')`,
						backgroundRepeat: "no-repeat",
						backgroundSize: "cover",
						backgroundPosition: "center",
					}}
				>
					<div className="container mx-auto px-4 md:px-6">
						<div className="text-center mb-12">
							<div
								id="contact-badge"
								className="inline-flex items-center gap-2 bg-brand-blue/10 px-4 py-2 rounded-full text-brand-blue font-semibold text-sm mb-4 animate-scroll-scale animate-scroll-stagger-1"
							>
								<Phone className="w-4 h-4" />
								{t("contact.badge")}
							</div>
							<h2 className="text-3xl md:text-4xl font-bold text-brand-blue mb-4 animate-scroll-fade-up animate-scroll-stagger-2">
								{t("contact.title")}
							</h2>
							<p className="text-lg text-dark-gray animate-scroll-fade-up animate-scroll-stagger-3">
								{t("contact.subtitle")}
							</p>
						</div>
						<div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
							<Card className="bg-gradient-to-br from-brand-white to-palm-green/10 border-0 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 group animate-scroll-slide-left animate-scroll-stagger-1">
								<CardHeader className="pb-4">
									<div className="flex items-center gap-4">
										<div className="w-16 h-16 bg-gradient-to-br from-palm-green to-mountain-green rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
											<MapPin className="text-white w-8 h-8" />
										</div>
										<div>
											<CardTitle className="text-brand-blue">{t("contact.guadeloupe")}</CardTitle>
											<p className="text-dark-gray">Jacky GIBRIEN</p>
										</div>
									</div>
								</CardHeader>
								<CardContent className="space-y-3">
									<a
										href="tel:+590690996000"
										className="flex items-center gap-3 p-3 bg-brand-white/50 rounded-xl hover:bg-palm-green/10 transition-colors duration-300 group"
									>
										<Phone className="w-5 h-5 text-palm-green" />
										<span className="group-hover:text-palm-green transition-colors">
											+590 690 99 60 00
										</span>
									</a>
									<a
										href="mailto:<EMAIL>"
										className="flex items-center gap-3 p-3 bg-brand-white/50 rounded-xl hover:bg-palm-green/10 transition-colors duration-300 group"
									>
										<Mail className="w-5 h-5 text-palm-green" />
										<span className="group-hover:text-palm-green transition-colors">
											<EMAIL>
										</span>
									</a>
								</CardContent>
							</Card>
							<Card className="bg-gradient-to-br from-brand-white to-brand-sky/10 border-0 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 group animate-scroll-slide-right animate-scroll-stagger-2">
								<CardHeader className="pb-4">
									<div className="flex items-center gap-4">
										<div className="w-16 h-16 bg-gradient-to-br from-brand-sky to-sky-gradient rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
											<MapPin className="text-white w-8 h-8" />
										</div>
										<div>
											<CardTitle className="text-brand-blue">{t("contact.france")}</CardTitle>
											<p className="text-dark-gray">Francis GIBRIEN</p>
										</div>
									</div>
								</CardHeader>
								<CardContent className="space-y-3">
									<a
										href="tel:+33681444406"
										className="flex items-center gap-3 p-3 bg-brand-white/50 rounded-xl hover:bg-brand-sky/10 transition-colors duration-300 group"
									>
										<Phone className="w-5 h-5 text-brand-sky" />
										<span className="group-hover:text-brand-sky transition-colors">
											+33 6 81 44 44 06
										</span>
									</a>
									<a
										href="mailto:<EMAIL>"
										className="flex items-center gap-3 p-3 bg-brand-white/50 rounded-xl hover:bg-brand-sky/10 transition-colors duration-300 group"
									>
										<Mail className="w-5 h-5 text-brand-sky" />
										<span className="group-hover:text-brand-sky transition-colors">
											<EMAIL>
										</span>
									</a>
								</CardContent>
							</Card>
						</div>
					</div>
				</section>
			</main>

			<footer className="bg-gradient-to-r from-green-600 via-green-700 to-green-800 text-white">
				<div className="container mx-auto py-8 px-4 md:px-6">
					<div className="text-center">
						<div className="flex items-center justify-center gap-3 mb-4">
							<div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center">
								<Image
									src="/odass-new-logo.png"
									alt="Logo Gran Fondo"
									width={40}
									height={40}
									className="rounded-lg"
								/>
							</div>
							<span className="font-bold text-xl">Gran Fondo Îles de Guadeloupe</span>
						</div>
						<p className="text-sm text-white/80">
							&copy; {new Date().getFullYear()} UCI Gran Fondo Îles de Guadeloupe. {t("footer.copyright")}
						</p>
					</div>
				</div>
			</footer>
		</div>
	);
}
